/**
 * API网关主服务
 */
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AppService implements OnModuleInit {
  private readonly logger = new Logger(AppService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject('SERVICE_REGISTRY') private readonly serviceRegistry: ClientProxy,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
    @Inject('ASSET_SERVICE') private readonly assetService: ClientProxy,
    @Inject('RENDER_SERVICE') private readonly renderService: ClientProxy,
  ) {}

  async onModuleInit() {
    // 等待所有微服务连接
    try {
      await Promise.all([
        this.serviceRegistry.connect(),
        this.userService.connect(),
        this.projectService.connect(),
        this.assetService.connect(),
        this.renderService.connect(),
      ]);
      this.logger.log('所有微服务连接成功');
    } catch (error) {
      this.logger.error('微服务连接失败', error);
    }
  }

  getInfo() {
    return {
      name: 'DL（Digital Learning）引擎API网关',
      version: '1.0.0',
      description: 'DL（Digital Learning）引擎API网关，作为前端和微服务之间的中介',
      environment: this.configService.get<string>('NODE_ENV', 'development'),
    };
  }

  async healthCheck() {
    const services = {
      gateway: { status: 'up' },
      serviceRegistry: { status: 'unknown' },
      userService: { status: 'unknown' },
      projectService: { status: 'unknown' },
      assetService: { status: 'unknown' },
      renderService: { status: 'unknown' },
    };

    // 使用HTTP健康检查而不是微服务消息
    const httpHealthChecks = [
      { name: 'serviceRegistry', url: 'http://service-registry:4010/api/health' },
      { name: 'userService', url: 'http://user-service:4001/api/health' },
      { name: 'projectService', url: 'http://project-service:4002/api/health' },
      { name: 'assetService', url: 'http://asset-service:4003/api/health' },
      { name: 'renderService', url: 'http://render-service:4004/api/health' },
    ];

    for (const check of httpHealthChecks) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(check.url, {
          method: 'GET',
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          services[check.name].status = 'up';
        } else {
          services[check.name].status = 'down';
        }
      } catch (error) {
        services[check.name].status = 'down';
        this.logger.error(`${check.name}健康检查失败`, error.message);
      }
    }

    const allUp = Object.values(services).every((service) => service.status === 'up');

    return {
      status: allUp ? 'up' : 'degraded',
      timestamp: new Date().toISOString(),
      services,
    };
  }
}
