/**
 * 项目服务主控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { AppService } from './app.service';

@ApiTags('项目服务')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '获取项目服务信息' })
  getInfo() {
    return this.appService.getInfo();
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  healthCheck() {
    return this.appService.healthCheck();
  }

  // 微服务健康检查
  @MessagePattern('health')
  microserviceHealthCheck() {
    return this.appService.healthCheck();
  }
}
