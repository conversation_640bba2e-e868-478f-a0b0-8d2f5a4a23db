/**
 * 服务注册中心主服务
 */
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private readonly configService: ConfigService) {}

  getInfo() {
    return {
      name: '服务注册中心',
      version: '1.0.0',
      description: 'DL（Digital Learning）引擎服务注册中心',
      environment: this.configService.get<string>('NODE_ENV', 'development'),
    };
  }

  healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }
}
